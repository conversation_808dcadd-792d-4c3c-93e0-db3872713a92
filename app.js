const express=require('express')
const jwt=require('jsonwebtoken')
const cookieParser=require('cookie-parser')
const bcrypt=require('bcrypt')
const path=require('path')
const postModel=require('./models/post')
const userModel=require('./models/user')
const app=express()

app.set("view engine",'ejs')
app.use(express.json())
app.use(express.urlencoded({extended:true}))
app.use(express.static(path.join(__dirname,'public')))
app.use(cookieParser())

app.get('/',(req,res)=>{
    res.render('index')
})

app.get('/login',(req,res)=>{
    res.render('login')
})

app.post('/register',async (req,res)=>{
    const {name,username,email,password,age}=req.body
    let user=await userModel.findOne({email})
    if(user) return res.status(400).json("user is already exists")

    bcrypt.genSalt(10,(err,salt)=>{
        bcrypt.hash(password,salt,async(err,hash)=>{
            const newUser = await userModel.create({
                username,
                name,
                age,
                email,
                password:hash
            })
            const token=jwt.sign({email:email,userid:newUser._id},"shoess")
            res.cookie('token',token)
            res.redirect('/login')
        })
    })
})

app.post('/login',async (req,res)=>{
    const {email,password} =req.body

    let user=await userModel.findOne({email})
    if(!user) return res.status(500).send("Something went wrong")

    bcrypt.compare(password,user.password,async(err,result)=>{
        if(result){
        let token=jwt.sign({email:email,userid:user._id},"shoess")
        res.cookie('token',token)
         return res.redirect('/profile')
        }else
        { res.redirect('/login')
        }
    })
})
app.get('/profile',isLoggedIn,async (req,res)=>{
    let user=await userModel.findOne({email:req.user.email})
    console.log(user)
    res.render('profile',{user})
})

app.get('/logout',(req,res)=>{
    res.cookie("token","")
    res.redirect('/login')
})

function isLoggedIn(req,res,next){
    if(req.cookies.token == '') {res.send("you must be Loggied")}
    else
    {
        let data=jwt.verify(req.cookies.token,"shoess")
            req.user=data
    }
    next()

}


app.post('/create-post',async (req,res)=>{
    const {content}=req.body
    let user=await userModel.findOne({email:req.user.email})
    let post=await postModel.create({
        user:user._id,
        content
    })
    user.post.push(post._id)
    await user.save()
    res.redirect('/profile')
})

app.listen(5000,()=>{
    console.log("server is running on 5000")
})