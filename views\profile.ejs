<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Post Creation App</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>
<body>
    <div class="w-full min-h-screen bg-zinc-800">
        <!-- Navbar -->
        <nav class="bg-zinc-900 shadow-lg">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <h1 class="text-white text-xl font-bold">Post Creation App</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-300">Welcome, <%= user.name %></span>
                        <a href="/logout" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition duration-200">
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Hero Section with Post Creation -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 py-16">
            <div class="max-w-4xl mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold text-white mb-4">Share Your Thoughts</h2>
                <p class="text-blue-100 mb-8">Create and share your posts with the community</p>

                <!-- Post Creation Form -->
                <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl mx-auto">
                    <form action="/createpost" method="POST">
                        <div class="mb-4">
                            <textarea
                                name="content"
                                placeholder="What's on your mind?"
                                class="w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                rows="4"
                                required
                            ></textarea>
                        </div>
                        <button
                            type="submit"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition duration-200 w-full sm:w-auto"
                        >
                            Create Post
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Posts Section -->
        <div class="max-w-4xl mx-auto px-4 py-8">
            <h3 class="text-2xl font-bold text-white mb-6">Your Posts</h3>

            <% if (user.post && user.post.length > 0) { %>
                <div class="grid gap-6">
                    <% user.post.forEach(function(post, index) { %>
                        <div class="bg-zinc-700 rounded-lg shadow-lg p-6">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold"><%= user.name.charAt(0).toUpperCase() %></span>
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="text-white font-semibold"><%= user.name %></h4>
                                        <p class="text-gray-400 text-sm">@<%= user.username %></p>
                                    </div>
                                </div>
                                <span class="text-gray-400 text-sm">Post #<%= index + 1 %></span>
                            </div>


                            <!-- Like and Edit Options -->
                            <div class="flex items-center justify-between pt-4 border-t border-zinc-600">
                                <div class="flex items-center space-x-4">
                                    <button class="flex items-center space-x-2 text-gray-400 hover:text-red-500 transition duration-200">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span><a href="/like/<%= post._id %>">Like</a></span>
                                    </button>

                                    <button class="flex items-center space-x-2 text-gray-400 hover:text-blue-500 transition duration-200">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        <span><a href="/update/<%= user._id %>"></a>Edit</a></span>
                                    </button>
                                </div>

                                <div class="flex items-center space-x-2 text-gray-400">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span class="text-sm">Just now</span>
                                </div>
                            </div>
                        </div>
                    <% }); %>
                </div>
            <% } else { %>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-zinc-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-2">No posts yet</h3>
                    <p class="text-gray-400">Create your first post using the form above!</p>
                </div>
            <% } %>
        </div>
    </div>
</body>
</html>